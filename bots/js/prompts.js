// prompts.js - Prompt Management Module (Read-only version)

let defaultPrompts = {
    welcome: "Welcome to HKBU Chat Assistant! I'm here to help you learn and explore. How can I assist you today?",
    system: "You are a helpful AI learning assistant at HKBU, designed to support students in their academic journey."
};

// Function to convert markdown to HTML
function formatMarkdown(text) {
    return text
        .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')  // Bold with **
        .replace(/\*(.*?)\*/g, '<em>$1</em>')             // Italic with *
        .replace(/_(.*?)_/g, '<em>$1</em>')               // Italic with _
        .replace(/\n/g, '<br>');                          // Line breaks
}

// Load prompts from files
async function loadPrompts() {
    try {
        const welcomeResponse = await fetch('../prompts/welcome.txt');
        const systemResponse = await fetch('../prompts/system.txt');
        
        if (welcomeResponse.ok && systemResponse.ok) {
            const welcomeText = await welcomeResponse.text();
            const systemText = await systemResponse.text();
            
            // Parse JSON if the response is JSON formatted
            try {
                const welcomeJson = JSON.parse(welcomeText);
                defaultPrompts.welcome = welcomeJson.content || welcomeText;
            } catch {
                defaultPrompts.welcome = welcomeText;
            }
            
            try {
                const systemJson = JSON.parse(systemText);
                defaultPrompts.system = systemJson.content || systemText;
            } catch {
                defaultPrompts.system = systemText;
            }
        }
    } catch (error) {
        console.log('Using default prompts:', error);
    }

    // Initialize displays with markdown formatting
    document.getElementById('welcome-display').innerHTML = formatMarkdown(defaultPrompts.welcome);
    document.getElementById('system-display').innerHTML = formatMarkdown(defaultPrompts.system);
    
    // Update global variables in core.js (keep plain text for API)
    welcomePrompt = defaultPrompts.welcome;
    systemPrompt = defaultPrompts.system;
}

// Toggle prompt sections
function togglePrompt(type) {
    const content = document.getElementById(`${type}-content`);
    const header = content.previousElementSibling;
    const icon = header.querySelector('.toggle-icon');
    
    if (content.classList.contains('hidden')) {
        content.classList.remove('hidden');
        icon.textContent = '▼';
    } else {
        content.classList.add('hidden');
        icon.textContent = '▶';
    }
}

// Initialize prompts when the page loads
document.addEventListener('DOMContentLoaded', () => {
    loadPrompts();
});

// Export necessary functions
window.togglePrompt = togglePrompt;