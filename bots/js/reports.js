// reports.js - Report Generation Module

function updateReportTimestamp() {
    const timestamp = new Date().toLocaleString();
    document.getElementById('report-timestamp').textContent = timestamp;
}

function generateReport() {
    if (chatHistory.length === 0) {
        showNotification('No conversation to report on', 'error');
        return;
    }

    const report = createReport();
    document.getElementById('report-content').innerHTML = report;
    updateReportTimestamp();
    document.getElementById('report-modal').style.display = 'flex';
}

function createReport() {
    const now = new Date();
    const duration = chatHistory.length > 0 ? 
        Math.round((chatHistory[chatHistory.length - 1].timestamp - chatHistory[0].timestamp) / 1000 / 60) : 0;
    
    const userMessages = chatHistory.filter(msg => msg.role === 'user');
    const assistantMessages = chatHistory.filter(msg => msg.role === 'assistant');
    
    let report = `
        <h2>📊 HKBU Learning Session Report</h2>
        <p><strong>Generated:</strong> ${now.toLocaleString()}</p>
        <p><strong>Duration:</strong> ${duration} minutes</p>
        <p><strong>Total Messages:</strong> ${chatHistory.length}</p>
        <p><strong>Your Messages:</strong> ${userMessages.length}</p>
        <p><strong>Assistant Responses:</strong> ${assistantMessages.length}</p>
        
        <h3>💡 Session Summary</h3>
        <p>${generateSummary()}</p>
        
        <h3>📈 Your Contribution Analysis</h3>
        <p>${analyzeContribution()}</p>
        
        <h3>📝 Complete Conversation</h3>
        <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; max-height: 400px; overflow-y: auto;">
    `;
    
    chatHistory.forEach(msg => {
        report += `
            <div style="margin-bottom: 15px; padding: 10px; background: ${msg.role === 'user' ? '#e3f2fd' : '#f1f8e9'}; border-radius: 6px;">
                <strong>${msg.role === 'user' ? '👤 You' : '🤖 Assistant'}:</strong><br>
                ${msg.content.replace(/\n/g, '<br>')}
                <div style="font-size: 0.8em; color: #666; margin-top: 5px;">
                    ${msg.timestamp.toLocaleTimeString()}
                </div>
            </div>
        `;
    });
    
    report += '</div>';
    
    report += `
        <hr style="margin: 20px 0;">
        <div style="text-align: center; font-size: 0.9rem; color: #666;">
            <strong>Created by:</strong> Dr. Simon Wang, Innovation Officer<br>
            Language Centre, Hong Kong Baptist University<br>
            <a href="mailto:<EMAIL>"><EMAIL></a>
        </div>
    `;
    
    return report;
}

function generateSummary() {
    const topics = extractTopics();
    const questionsAsked = chatHistory.filter(msg => 
        msg.role === 'user' && msg.content.includes('?')
    ).length;
    
    return `This learning session covered ${topics.length > 0 ? topics.join(', ') : 'various topics'}. 
            You asked ${questionsAsked} questions and engaged in ${Math.floor(chatHistory.length / 2)} conversation exchanges. 
            The session demonstrated active learning through inquiry and discussion.`;
}

function analyzeContribution() {
    const userMessages = chatHistory.filter(msg => msg.role === 'user');
    if (userMessages.length === 0) return 'No user messages to analyze.';
    
    const avgLength = userMessages.reduce((sum, msg) => sum + msg.content.length, 0) / userMessages.length;
    const questionsRatio = userMessages.filter(msg => msg.content.includes('?')).length / userMessages.length;
    
    let analysis = '';
    
    if (avgLength > 100) {
        analysis += 'You provided detailed and thoughtful messages. ';
    } else if (avgLength > 50) {
        analysis += 'You engaged with good depth in your responses. ';
    } else {
        analysis += 'You kept your messages concise and focused. ';
    }
    
    if (questionsRatio > 0.5) {
        analysis += 'You showed excellent curiosity by asking many questions. ';
    } else if (questionsRatio > 0.2) {
        analysis += 'You balanced questions with statements effectively. ';
    }
    
    analysis += 'Your engagement shows a positive learning attitude and willingness to explore topics deeply.';
    
    return analysis;
}

function extractTopics() {
    const commonWords = ['the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should', 'can', 'may', 'might', 'must'];
    const allWords = chatHistory
        .filter(msg => msg.role === 'user')
        .map(msg => msg.content.toLowerCase())
        .join(' ')
        .replace(/[^\w\s]/g, '')
        .split(' ')
        .filter(word => word.length > 3 && !commonWords.includes(word));
    
    const wordCount = {};
    allWords.forEach(word => {
        wordCount[word] = (wordCount[word] || 0) + 1;
    });
    
    return Object.entries(wordCount)
        .sort((a, b) => b[1] - a[1])
        .slice(0, 5)
        .map(([word]) => word);
}

function downloadPDF() {
    const { jsPDF } = window.jspdf;
    const doc = new jsPDF();
    
    // Set font
    doc.setFontSize(20);
    doc.text('HKBU Learning Session Report', 20, 20);
    
    doc.setFontSize(12);
    let yPos = 40;
    
    // Add report metadata
    const now = new Date();
    doc.text(`Generated: ${now.toLocaleString()}`, 20, yPos);
    yPos += 10;
    
    const duration = chatHistory.length > 0 ? 
        Math.round((chatHistory[chatHistory.length - 1].timestamp - chatHistory[0].timestamp) / 1000 / 60) : 0;
    doc.text(`Duration: ${duration} minutes`, 20, yPos);
    yPos += 10;
    doc.text(`Total Messages: ${chatHistory.length}`, 20, yPos);
    yPos += 20;
    
    // Add section headers and content
    doc.setFontSize(14);
    doc.text('Session Summary', 20, yPos);
    yPos += 10;
    
    doc.setFontSize(11);
    const summaryText = generateSummary().replace(/[^\x00-\x7F]/g, ''); // Remove non-ASCII
    const summaryLines = doc.splitTextToSize(summaryText, 170);
    summaryLines.forEach(line => {
        if (yPos > 270) {
            doc.addPage();
            yPos = 20;
        }
        doc.text(line, 20, yPos);
        yPos += 6;
    });
    
    yPos += 10;
    doc.setFontSize(14);
    doc.text('Complete Conversation', 20, yPos);
    yPos += 10;
    
    // Add conversation
    doc.setFontSize(11);
    chatHistory.forEach((msg, index) => {
        if (yPos > 270) {
            doc.addPage();
            yPos = 20;
        }
        
        // Add role without emoji
        const role = msg.role === 'user' ? 'You:' : 'Assistant:';
        doc.setFont(undefined, 'bold');
        doc.text(role, 20, yPos);
        doc.setFont(undefined, 'normal');
        yPos += 6;
        
        // Add message content (remove emojis and special characters)
        const cleanContent = msg.content.replace(/[^\x00-\x7F]/g, '');
        const lines = doc.splitTextToSize(cleanContent, 170);
        
        lines.forEach(line => {
            if (yPos > 270) {
                doc.addPage();
                yPos = 20;
            }
            doc.text(line, 20, yPos);
            yPos += 6;
        });
        
        // Add timestamp
        doc.setFontSize(9);
        doc.text(msg.timestamp.toLocaleTimeString(), 20, yPos);
        doc.setFontSize(11);
        yPos += 10;
    });
    
    // Add footer
    if (yPos > 250) {
        doc.addPage();
        yPos = 20;
    }
    yPos += 10;
    doc.setFontSize(10);
    doc.text('Created by: Dr. Simon Wang, Innovation Officer', 20, yPos);
    yPos += 5;
    doc.text('Language Centre, Hong Kong Baptist University', 20, yPos);
    yPos += 5;
    doc.text('<EMAIL>', 20, yPos);
    
    // Save the PDF
    doc.save(`HKBU_Learning_Report_${new Date().toISOString().split('T')[0]}.pdf`);
}

function downloadMarkdown() {
    const report = createMarkdownReport();
    const blob = new Blob([report], { type: 'text/markdown' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `HKBU_Learning_Report_${new Date().toISOString().split('T')[0]}.md`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
}

function createMarkdownReport() {
    const now = new Date();
    const duration = chatHistory.length > 0 ? 
        Math.round((chatHistory[chatHistory.length - 1].timestamp - chatHistory[0].timestamp) / 1000 / 60) : 0;
    
    let markdown = `# 📊 HKBU Learning Session Report

**Generated:** ${now.toLocaleString()}
**Duration:** ${duration} minutes
**Total Messages:** ${chatHistory.length}

## 💡 Session Summary

${generateSummary()}

## 📈 Your Contribution Analysis

${analyzeContribution()}

## 📝 Complete Conversation

`;
    
    chatHistory.forEach(msg => {
        const role = msg.role === 'user' ? '👤 **You**' : '🤖 **Assistant**';
        markdown += `### ${role} (${msg.timestamp.toLocaleTimeString()})\n\n${msg.content}\n\n`;
    });
    
    markdown += `---
*Created by: Dr. Simon Wang, Innovation Officer*  
*Language Centre, Hong Kong Baptist University*  
*<EMAIL>*`;
    
    return markdown;
}

function copyReport() {
    const reportText = document.getElementById('report-content').innerText;
    
    navigator.clipboard.writeText(reportText)
        .then(() => {
            showNotification('Report copied to clipboard!', 'success');
        })
        .catch(err => {
            // Fallback for older browsers
            const textArea = document.createElement('textarea');
            textArea.value = reportText;
            textArea.style.position = 'fixed';
            textArea.style.left = '-999999px';
            document.body.appendChild(textArea);
            textArea.select();
            
            try {
                document.execCommand('copy');
                showNotification('Report copied to clipboard!', 'success');
            } catch (err) {
                showNotification('Failed to copy report', 'error');
            }
            
            document.body.removeChild(textArea);
        });
}

function closeReport() {
    document.getElementById('report-modal').style.display = 'none';
}