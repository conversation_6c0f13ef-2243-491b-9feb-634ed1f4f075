* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Robot<PERSON>, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    padding: 20px;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    background: white;
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0,0,0,0.1);
    overflow: hidden;
    display: grid;
    grid-template-columns: 350px 1fr;
    height: 90vh;
    min-height: 600px;
    position: relative; /* Added */
    transition: grid-template-columns 0.3s ease; /* Added */
}

/* Add this new class */
.container.sidebar-collapsed {
    grid-template-columns: 0 1fr;
}

/* Add responsive design */
@media (max-width: 768px) {
    .container {
        grid-template-columns: 1fr;
        height: 100vh;
        border-radius: 0;
        margin: 0;
    }
    
    .container.sidebar-collapsed {
        grid-template-columns: 1fr;
    }
}

.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 12px 20px;
    border-radius: 8px;
    color: white;
    font-size: 0.9rem;
    z-index: 3000;
    animation: slideIn 0.3s ease-out;
}

.notification.success { background: #28a745; }
.notification.error { background: #dc3545; }
.notification.info { background: #17a2b8; }

@keyframes slideIn {
    from { transform: translateX(100%); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

@media (max-width: 768px) {
    .container {
        grid-template-columns: 1fr;
        grid-template-rows: auto 1fr;
    }
}

/* Report Modal Styles */
.report-modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 2000;
    justify-content: center;
    align-items: center;
    padding: 20px;
}

.report-content-wrapper {
    background: white;
    border-radius: 12px;
    max-width: 800px;
    max-height: 90vh;
    width: 100%;
    display: flex;
    flex-direction: column;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.report-header {
    padding: 20px;
    border-bottom: 1px solid #e0e0e0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.report-header h2 {
    margin: 0;
    color: #333;
}

.report-body {
    padding: 20px;
    overflow-y: auto;
    flex: 1;
}

.report-footer {
    padding: 20px;
    border-top: 1px solid #e0e0e0;
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
    justify-content: center;
}

.report-timestamp {
    text-align: center;
    padding: 10px;
    font-size: 0.9rem;
    color: #666;
    border-top: 1px solid #e0e0e0;
}

.close-btn {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #666;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    transition: all 0.2s;
}

.close-btn:hover {
    background: #f0f0f0;
    color: #333;
}