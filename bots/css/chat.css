.chat-area {
    display: flex;
    flex-direction: column;
    height: 100%;
}

.chat-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px;
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.chat-messages {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
    background: #f8f9fa;
    max-height: calc(100vh - 300px);
    min-height: 300px;
    scroll-behavior: smooth;
}

.message {
    margin-bottom: 15px;
    display: flex;
    align-items: flex-start;
    animation: fadeIn 0.3s ease-in;
}

.message.user { 
    flex-direction: row-reverse; 
}

.message-content {
    max-width: min(70%, 500px);
    padding: 12px 16px;
    border-radius: 18px;
    word-wrap: break-word;
    white-space: pre-wrap;
    overflow-wrap: break-word;
    line-height: 1.4;
    word-break: break-word;
    hyphens: auto;
}

.message.user .message-content {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    margin-right: 10px;
}

.message.assistant .message-content {
    background: white;
    color: #333;
    border: 1px solid #e0e0e0;
    margin-left: 10px;
}

.chat-input-container {
    padding: 20px;
    background: white;
    border-top: 1px solid #e0e0e0;
}

.chat-input-wrapper {
    display: flex;
    gap: 10px;
    align-items: flex-end;
}

.chat-input {
    flex: 1;
    border: 2px solid #e0e0e0;
    border-radius: 25px;
    padding: 12px 20px;
    font-size: 1rem;
    outline: none;
    resize: none;
    max-height: 100px;
    min-height: 45px;
}

.chat-input:focus { 
    border-color: #667eea; 
}

.input-buttons {
    display: flex;
    gap: 10px;
}

.send-btn, .done-btn {
    border: none;
    border-radius: 50%;
    width: 45px;
    height: 45px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    transition: transform 0.2s;
}

.send-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.done-btn {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
}

.send-btn:hover, .done-btn:hover { 
    transform: scale(1.05); 
}

.send-btn:disabled, .done-btn:disabled { 
    opacity: 0.5; 
    cursor: not-allowed; 
    transform: none; 
}

.report-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 2000;
}

.report-content {
    background: white;
    border-radius: 12px;
    padding: 30px;
    max-width: 800px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
    position: relative;
}

.close-btn {
    position: absolute;
    top: 10px;
    right: 10px;
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #666;
}

.close-btn:hover {
    color: #333;
}

/* Add after your existing chat.css content */

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    .chat-area {
        background: #1a1a1a;
    }
    
    .message.assistant .message-content {
        background: #2d2d2d;
        color: #fff;
        border-color: #404040;
    }
    
    .chat-input {
        background: #2d2d2d;
        color: #fff;
    }
}

/* Loading State Styles */
.message-loading {
    display: flex;
    gap: 4px;
    padding: 8px;
}

.loading-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #667eea;
    animation: bounce 0.5s infinite alternate;
}

@keyframes bounce {
    from { transform: translateY(0); }
    to { transform: translateY(-5px); }
}

/* Accessibility Improvements */
.chat-input:focus,
.send-btn:focus,
.done-btn:focus {
    outline: 2px solid #667eea;
    outline-offset: 2px;
}

.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    border: 0;
}

/* Responsive Font Sizes */
@media (max-width: 480px) {
    .chat-header {
        font-size: 0.9rem;
    }
    
    .message-content {
        font-size: 0.9rem;
    }
    
    .chat-input {
        font-size: 0.9rem;
    }
}

/* Scrollbar Styling */
.chat-messages::-webkit-scrollbar {
    width: 8px;
}

.chat-messages::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

.chat-messages::-webkit-scrollbar-thumb {
    background: #888;
    border-radius: 4px;
}

.chat-messages::-webkit-scrollbar-thumb:hover {
    background: #555;
}

/* Code Block Styling */
.message-content pre {
    background: #f8f9fa;
    padding: 12px;
    border-radius: 8px;
    overflow-x: auto;
    margin: 8px 0;
}

.message-content code {
    font-family: 'Courier New', monospace;
    font-size: 0.9em;
}

/* Report Modal Styles */
.report-modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    z-index: 2000;
    justify-content: center;
    align-items: center;
}

.report-content {
    background: white;
    border-radius: 12px;
    width: 90%;
    max-width: 800px;
    max-height: 90vh;
    display: flex;
    flex-direction: column;
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
}

.report-header {
    padding: 20px;
    border-bottom: 1px solid #eee;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.report-header h3 {
    margin: 0;
    color: #333;
    font-size: 1.25rem;
}

.report-body {
    padding: 20px;
    overflow-y: auto;
    flex: 1;
    min-height: 200px;
    max-height: calc(90vh - 180px);
}

.report-footer {
    padding: 20px;
    border-top: 1px solid #eee;
    background: #f8f9fa;
    border-bottom-left-radius: 12px;
    border-bottom-right-radius: 12px;
}

.report-actions {
    display: flex;
    gap: 10px;
    justify-content: center;
    margin-bottom: 10px;
}

.report-info {
    text-align: center;
    color: #666;
    font-size: 0.9rem;
}

.btn-icon {
    margin-right: 8px;
}

.close-btn {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #666;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: background-color 0.2s;
}

.close-btn:hover {
    background-color: #f0f0f0;
    color: #333;
}

@media (max-width: 576px) {
    .report-content {
        width: 95%;
        margin: 10px;
    }

    .report-actions {
        flex-direction: column;
    }

    .btn {
        width: 100%;
    }
}

/* Prompt Edit Modal Styles */
.prompt-edit-modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    z-index: 2000;
    justify-content: center;
    align-items: center;
}

.prompt-edit-content {
    background: white;
    border-radius: 12px;
    padding: 20px;
    width: 90%;
    max-width: 800px;
    max-height: 90vh;
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.prompt-edit-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.prompt-edit-textarea {
    width: 100%;
    height: 300px;
    padding: 15px;
    border: 1px solid #ddd;
    border-radius: 8px;
    font-size: 1rem;
    line-height: 1.5;
    resize: vertical;
}

.prompt-edit-buttons {
    display: flex;
    gap: 10px;
    justify-content: flex-end;
}

.chat-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.chat-header-main {
    flex: 1;
}

.new-session-btn {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 6px;
    padding: 8px 16px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: all 0.2s ease;
}

.new-session-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-1px);
}

.new-session-btn .btn-icon {
    font-size: 14px;
}