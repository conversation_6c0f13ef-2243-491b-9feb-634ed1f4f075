# app.py
from flask import Flask, request, jsonify, send_file
from flask import redirect
from flask_cors import CORS
import requests
import json
import os
import resend
import re
import dotenv
import weasyprint

app = Flask(__name__)
CORS(app)

@app.route('/')
def index():
    return jsonify({'status': 'HKBU Student Chatbot API is running!', 'endpoints': ['/api/chat', '/api/test', '/basicBot', '/frontend', '/embed']})

# Add the embed route
@app.route('/embed')
def serve_embed():
    return send_file('bots/basicBot.html')

@app.route('/embed/<bot_name>')
def serve_embed_bot(bot_name):
    # Define allowed bots for security
    allowed_bots = {
        'basicBot': 'bots/basicBot.html',
        'videoHelper': 'bots/videoHelper/bot.html',  # Future bot
        'writingTutor': 'bots/writingTutor/bot.html',  # Future bot
    }
    
    if bot_name in allowed_bots:
        return send_file(allowed_bots[bot_name])
    else:
        return "Bot not found", 404
    
# Add these routes to handle CSS/JS for embedded bots
@app.route('/embed/css/<filename>')
def serve_embed_css(filename):
    return send_file(f'bots/css/{filename}')

@app.route('/embed/js/<filename>')
def serve_embed_js(filename):
    return send_file(f'bots/js/{filename}')

# Add these to handle the nested bot paths
@app.route('/embed/bots/css/<filename>')
def serve_embed_bots_css(filename):
    return send_file(f'bots/css/{filename}')

@app.route('/embed/bots/js/<filename>')
def serve_embed_bots_js(filename):
    return send_file(f'bots/js/{filename}')

# Also handle prompts for embedded bots
@app.route('/embed/prompts/<filename>')
def serve_embed_prompts(filename):
    return send_file(f'prompts/{filename}')

# Add or update the after_request function to allow iframe embedding
@app.after_request
def after_request(response):
    # Remove X-Frame-Options to allow embedding
    response.headers.pop('X-Frame-Options', None)
    # Add CORS headers
    response.headers['Access-Control-Allow-Origin'] = '*'
    # Allow iframe from any origin
    response.headers['Content-Security-Policy'] = "frame-ancestors *;"
    return response



@app.route('/api/chat', methods=['POST', 'OPTIONS'])
def chat():
    if request.method == 'OPTIONS':
        # Handle preflight CORS request
        response = jsonify({'status': 'OK'})
        response.headers.add('Access-Control-Allow-Origin', '*')
        response.headers.add('Access-Control-Allow-Headers', 'Content-Type')
        response.headers.add('Access-Control-Allow-Methods', 'POST, OPTIONS')
        return response
    
    try:
        data = request.json
        user_message = data.get('message', '') # pyright: ignore[reportOptionalMemberAccess]
        api_key = data.get('apiKey', '')  # type: ignore # Note: frontend sends 'apiKey'
        provider = data.get('provider', 'hkbu') # type: ignore
        model = data.get('model', 'gpt-4.1') # pyright: ignore[reportOptionalMemberAccess]
        system_prompt = data.get('systemPrompt', '') # pyright: ignore[reportOptionalMemberAccess]
        
        if not api_key:
            return jsonify({'error': 'No API key provided'}), 400
        
        if not user_message:
            return jsonify({'error': 'No message provided'}), 400
        
        # Route to appropriate API based on provider
        if provider == 'hkbu':
            response_text = call_hkbu_api(user_message, api_key, model, system_prompt)
        elif provider == 'openrouter':
            response_text = call_openrouter_api(user_message, api_key, model, system_prompt)
        else:
            return jsonify({'error': 'Unsupported provider'}), 400
        
        return jsonify({
            'response': response_text
        })
        
    except Exception as e:
        return jsonify({
            'error': str(e)
        }), 400

@app.route('/api/test', methods=['POST'])
def test_connection():
    try:
        data = request.json
        api_key = data.get('apiKey', '') # pyright: ignore[reportOptionalMemberAccess]
        provider = data.get('provider', 'hkbu') # pyright: ignore[reportOptionalMemberAccess]
        model = data.get('model', 'gpt-4.1') # pyright: ignore[reportOptionalMemberAccess]
        
        if not api_key:
            return jsonify({'error': 'No API key provided'}), 400
        
        # Test with a simple message
        if provider == 'hkbu':
            response = call_hkbu_api('Hello, this is a test.', api_key, model, 'You are a helpful assistant.')
        elif provider == 'openrouter':
            response = call_openrouter_api('Hello, this is a test.', api_key, model, 'You are a helpful assistant.')
        else:
            return jsonify({'error': 'Unsupported provider'}), 400
        
        return jsonify({
            'response': f'Connection successful! Response: {response[:50]}...'
        })
        
    except Exception as e:
        return jsonify({
            'error': str(e)
        }), 400

@app.route('/prompts/<filename>')
def serve_prompts(filename):
    try:
        with open(f'prompts/{filename}', 'r', encoding='utf-8') as file:
            content = file.read()
        return jsonify({'content': content})
    except FileNotFoundError:
        return jsonify({'error': f'Prompt file {filename} not found'}), 404
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/basicBot')
def serve_basic_bot():
    return send_file('bots/basicBot.html')

# ADD THESE NEW ROUTES HERE:
@app.route('/css/<filename>')
def serve_css(filename):
    return send_file(f'bots/css/{filename}')

@app.route('/js/<filename>')
def serve_js(filename):
    return send_file(f'bots/js/{filename}')

@app.route('/frontend')
def serve_frontend():
    return send_file('index.html')

@app.route('/api/sendEmail', methods=['POST'])
def send_email():
    try:
        data = request.json
        student_email = data.get('student_email')
        teacher_email = data.get('teacher_email')
        report_html = data.get('report_html')

        # Input validation
        if not student_email:
            return jsonify({'success': False, 'error': 'Student email is required'}), 400

        if not teacher_email:
            return jsonify({'success': False, 'error': 'Teacher email is required'}), 400

        if not report_html:
            return jsonify({'success': False, 'error': 'Report is required'}), 400

        # Validate email format (basic validation)
        email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        if not re.match(email_pattern, student_email):
            return jsonify({'success': False, 'error': 'Invalid student email format'}), 400

        if not re.match(email_pattern, teacher_email):
            return jsonify({'success': False, 'error': 'Invalid teacher email format'}), 400

        # Check API key
        Resend_API_KEY = os.getenv('RESEND_API_KEY')
        if not Resend_API_KEY:
            return jsonify({'success': False, 'error': 'Resend API key not configured'}), 500

        resend.api_key = Resend_API_KEY
        params: resend.Emails.SendParams = {
            "from": "<EMAIL>",
            "to": [student_email],
            "cc": [teacher_email],
            "attachments": [
                {
                    "filename": "report.pdf",
                    "content": html_to_pdf(report_html)
                }
            ],
            "subject": "HKBU Chatbot Report",
            "html": email_template
        }
        email = resend.Emails.send(params)
        return jsonify({'success': True, 'message': 'Email sent successfully!'})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 400


email_template = """<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title>HKBU Chatbot Report</title>
    <style>
        /* 重置默认样式，确保邮件客户端一致性 */
        body {
            margin: 0;
            padding: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Arial, sans-serif;
            color: #333333;
            background-color: #f5f5f5;
            line-height: 1.6;
        }
        * {
            box-sizing: border-box;
        }
        /* 容器 */
        .container {
            max-width: 600px;
            margin: 0 auto;
            background-color: #ffffff;
            border: 1px solid #e0e0e0;
        }
        /* 标题区域 */
        .header {
            background-color: #f5f5f5;
            padding: 20px;
            text-align: center;
        }
        .header img {
            max-width: 150px;
            height: auto;
            display: block;
            margin: 0 auto;
        }
        /* 内容区域 */
        .content {
            padding: 20px;
        }
        .content p {
            margin: 0 0 15px;
            font-size: 16px;
        }
        /* 按钮 */
        .button {
            display: inline-block;
            padding: 12px 24px;
            background-color: #007bff;
            color: #ffffff !important;
            text-decoration: none;
            border-radius: 4px;
            font-size: 16px;
            font-weight: 500;
            text-align: center;
        }
        .button:hover {
            background-color: #0056b3;
        }
        /* 页脚 */
        .footer {
            padding: 15px;
            text-align: center;
            font-size: 12px;
            color: #777777;
            background-color: #f5f5f5;
            border-top: 1px solid #e0e0e0;
        }
        .footer a {
            color: #777777;
            text-decoration: underline;
        }
        /* 响应式设计 */
        @media only screen and (max-width: 600px) {
            .container {
                width: 100% !important;
                border: none;
            }
            .content {
                padding: 15px;
            }
            .button {
                width: 100%;
                box-sizing: border-box;
            }
        }
    </style>
</head>
<body>
    <table role="presentation" width="100%" cellspacing="0" cellpadding="0" style="background-color: #f5f5f5;">
        <tr>
            <td align="center">
                <table role="presentation" class="container" width="100%" style="max-width: 600px;" cellspacing="0" cellpadding="0">
                    <tr>
                        <td class="content">
                            <p>Dear Student,</p>
                            <p>Your Chatbot report is ready. Please find the attached PDF.</p>
                            <p>If you have any questions, contact us at <a href="mailto:<EMAIL>"><EMAIL></a>.</p>
                        </td>
                    </tr>
                    <tr>
                        <td class="footer">
                            <p>
                                HKBU Chatbot | &copy; 2025<br>
                            </p>
                        </td>
                    </tr>
                </table>
            </td>
        </tr>
    </table>
</body>
</html>
"""


def call_hkbu_api(message, api_key, model, system_prompt):
    """Call HKBU GenAI API"""
    base_url = "https://genai.hkbu.edu.hk/api/v0/rest"
    api_version = "2024-12-01-preview"
    url = f"{base_url}/deployments/{model}/chat/completions?api-version={api_version}"
    
    # Prepare messages
    messages = []
    if system_prompt.strip():
        messages.append({"role": "system", "content": system_prompt})
    messages.append({"role": "user", "content": message})
    
    headers = {
        "Content-Type": "application/json",
        "api-key": api_key
    }
    
    payload = {
        "messages": messages,
        "max_tokens": 1000,
        "temperature": 0.7,
        "top_p": 0.9,
        "stream": False
    }
    
    try:
        print(f"HKBU API Request to: {url}")
        response = requests.post(url, headers=headers, json=payload, timeout=30)
        print(f"HKBU Response status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            if "choices" in result and len(result["choices"]) > 0:
                return result["choices"][0]["message"]["content"]
            else:
                raise Exception("Invalid response format from HKBU API")
        elif response.status_code == 401:
            raise Exception("HKBU API: Invalid API key or access denied")
        elif response.status_code == 404:
            raise Exception(f"HKBU API: Model '{model}' not found")
        else:
            error_text = response.text
            raise Exception(f"HKBU API Error {response.status_code}: {error_text}")
            
    except requests.exceptions.Timeout:
        raise Exception("HKBU API: Request timeout")
    except requests.exceptions.RequestException as e:
        raise Exception(f"HKBU API: Network error - {str(e)}")

def call_openrouter_api(message, api_key, model, system_prompt):
    """Call OpenRouter API"""
    url = "https://openrouter.ai/api/v1/chat/completions"
    
    # Prepare messages
    messages = []
    if system_prompt.strip():
        messages.append({"role": "system", "content": system_prompt})
    messages.append({"role": "user", "content": message})
    
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {api_key}",
        "HTTP-Referer": "https://smartlessons.hkbu.tech",
        "X-Title": "HKBU Student Chatbot"
    }
    
    payload = {
        "model": model,
        "messages": messages,
        "max_tokens": 1000,
        "temperature": 0.7,
        "top_p": 0.9
    }
    
    try:
        print(f"OpenRouter API Request to: {url}")
        response = requests.post(url, headers=headers, json=payload, timeout=30)
        print(f"OpenRouter Response status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            if "choices" in result and len(result["choices"]) > 0:
                return result["choices"][0]["message"]["content"]
            else:
                raise Exception("Invalid response format from OpenRouter API")
        elif response.status_code == 401:
            raise Exception("OpenRouter API: Invalid API key")
        elif response.status_code == 402:
            raise Exception("OpenRouter API: Insufficient credits")
        else:
            error_text = response.text
            raise Exception(f"OpenRouter API Error {response.status_code}: {error_text}")
            
    except requests.exceptions.Timeout:
        raise Exception("OpenRouter API: Request timeout")
    except requests.exceptions.RequestException as e:
        raise Exception(f"OpenRouter API: Network error - {str(e)}")

def html_to_pdf(html_content):
    """Convert HTML to PDF and return base64 encoded content"""
    import base64
    from io import BytesIO

    try:
        # Create a complete HTML document with proper structure
        complete_html = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <style>
                body {{
                    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Arial, sans-serif;
                    line-height: 1.6;
                    color: #333;
                    max-width: 800px;
                    margin: 0 auto;
                    padding: 20px;
                }}
                h1, h2, h3 {{
                    color: #2c3e50;
                    margin-top: 30px;
                    margin-bottom: 15px;
                }}
                h1 {{
                    font-size: 24px;
                    text-align: center;
                    border-bottom: 2px solid #3498db;
                    padding-bottom: 10px;
                }}
                h2 {{
                    font-size: 20px;
                    color: #34495e;
                }}
                h3 {{
                    font-size: 16px;
                    color: #7f8c8d;
                }}
                p {{
                    margin-bottom: 10px;
                }}
                .message-container {{
                    background: #f8f9fa;
                    padding: 15px;
                    border-radius: 8px;
                    margin: 10px 0;
                }}
                .user-message {{
                    background: #e3f2fd;
                    border-left: 4px solid #2196f3;
                }}
                .assistant-message {{
                    background: #f1f8e9;
                    border-left: 4px solid #4caf50;
                }}
                .timestamp {{
                    font-size: 12px;
                    color: #666;
                    margin-top: 5px;
                }}
                .footer {{
                    text-align: center;
                    font-size: 12px;
                    color: #666;
                    margin-top: 30px;
                    padding-top: 20px;
                    border-top: 1px solid #ddd;
                }}
            </style>
        </head>
        <body>
            {html_content}
        </body>
        </html>
        """

        # Convert HTML to PDF using WeasyPrint
        html_doc = weasyprint.HTML(string=complete_html)
        pdf_bytes = html_doc.write_pdf()

        # Encode as base64
        base64_content = base64.b64encode(pdf_bytes).decode('utf-8')

        return base64_content
    except Exception as e:
        raise Exception(f"Error converting HTML to PDF: {str(e)}")

if __name__ == '__main__':
    print("🚀 Starting HKBU Student Chatbot Server...")
    print("📝 Available endpoints:")
    print("   GET  / - API status")
    print("   GET  /frontend - Original web interface")
    print("   GET  /basicBot - Advanced chatbot interface")
    print("   POST /api/chat - Chat with AI")
    print("   POST /api/test - Test API connection")
    print("   GET  /prompts/<filename> - Serve prompt files")
    print("\n🔧 Make sure to:")
    print("   1. Install: pip install flask flask-cors requests")
    print("   2. Get HKBU API key: https://genai.hkbu.edu.hk/settings/api-docs")
    print("   3. Get OpenRouter key: https://openrouter.ai/keys")
    
    # Use port 5000 for Railway
    port = 5001
    app.run(host='0.0.0.0', port=port, debug=False)