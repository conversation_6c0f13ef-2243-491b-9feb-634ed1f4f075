# Changelog - HKBU Chatbot Project

## [1.0.0] - 2024-08-31

### Added
- **Custom Domain**: Deployed to https://aitutor.hkbu.tech with SSL
- **iframe Embedding Support**: 
  - Added `/embed` and `/embed/<bot_name>` routes
  - Configured CORS headers for cross-origin embedding
  - Fixed CSS/JS loading for nested embed paths
- **Read-only Prompts**: Removed edit/reset functionality for cleaner UI
- **Markdown Support**: Added formatting for welcome/system messages
- **Multiple Bot Infrastructure**: Ready for videoHelper, writingTutor, etc.

### Changed
- Simplified UI by removing edit buttons
- Updated prompts to load from `prompts/welcome.txt` and `prompts/system.txt`
- Fixed JSON parsing for prompt files
- Connected model selection dropdown to API calls

### Technical Updates
- Railway deployment from tesolchina/hkbuchat repository
- DNS: CNAME aitutor → d5r8sz8c.up.railway.app (Aliyun)
- Fixed duplicate route errors in Flask
- Added routes for embedded asset serving

### Known Issues
- Minimum width 900px required for proper display
- Local file iframe testing blocked by CORS (use local server)

### Next Session TODO:
1. Create testing branch before making changes
2. Set up local testing environment
3. Create cleaner embed view without sidebar
4. Implement multiple bot configurations


cd /workspaces/hkbuchat/hkbuchat
git checkout -b feature/testing-new-features


# Install dependencies
pip install -r requirements.txt

# Run local server
python main.py

# Access at: http://localhost:5000/basicBot

# For iframe testing, create test.html and serve it:
python -m http.server 8080
# Open: http://localhost:8080/test.html


Right now we've got a working bot basicBot.html which can be found https://aitutor.hkbu.tech/basicBot 
for iFrame embedding 

<iframe 
    src="https://aitutor.hkbu.tech/embed/basicBot" 
    width="100%" 
    height="800"
    style="border: none; min-width: 900px;">
</iframe>


